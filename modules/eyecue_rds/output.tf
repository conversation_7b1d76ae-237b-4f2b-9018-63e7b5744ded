# AWS Account ID

output "account_id" {
  value = data.aws_caller_identity.current.account_id
}

# AWS IP Configuration Info
output "aws_ip_configuration" {
  description = "AWS IP configuration details"
  value = {
    prefix_list_enabled = var.enable_prefix_list
    region              = local.current_region
    total_ips           = var.enable_prefix_list ? local.total_ip_count : 0
    ec2_ranges          = var.enable_prefix_list ? length(local.aws_ec2_ranges) : 0
    corporate_ips       = var.enable_prefix_list ? length(local.corporate_cidrs) : 0
    prefix_list_id      = var.enable_prefix_list ? aws_ec2_managed_prefix_list.allowed_ips[0].id : null
    security_group_id   = aws_security_group.main_sec_group.id
    includes_global     = var.enable_prefix_list
    security_mode       = var.enable_prefix_list ? "prefix_list" : "open_0.0.0.0/0"
  }
}

output "prefix_list_details" {
  description = "Details of created prefix list (null if prefix list is disabled)"
  value = var.enable_prefix_list ? {
    id          = aws_ec2_managed_prefix_list.allowed_ips[0].id
    name        = aws_ec2_managed_prefix_list.allowed_ips[0].name
    arn         = aws_ec2_managed_prefix_list.allowed_ips[0].arn
    entry_count = local.total_ip_count
  } : null
}


# Master
output "master_db_instance_address" {
  description = "The address of the RDS instance"
  value       = module.master.db_instance_address
}

output "master_db_instance_arn" {
  description = "The ARN of the RDS instance"
  value       = module.master.db_instance_arn
}

output "master_db_instance_availability_zone" {
  description = "The availability zone of the RDS instance"
  value       = module.master.db_instance_availability_zone
}

output "master_db_instance_endpoint" {
  description = "The connection endpoint"
  value       = module.master.db_instance_endpoint
}

output "master_db_instance_hosted_zone_id" {
  description = "The canonical hosted zone ID of the DB instance (to be used in a Route 53 Alias record)"
  value       = module.master.db_instance_hosted_zone_id
}

output "master_db_instance_id" {
  description = "The RDS instance ID"
  value       = module.master.db_instance_id
}

output "master_db_instance_identifier" {
  description = "The RDS master DB instance identifier"
  # Module "terraform-aws-modules/rds/aws:5.2.0" doesn't output DBInstanceIdentifier, hence
  # extracting from ARN.
  value = try(regex(".*:db:([^:]+)$", module.master.db_instance_arn)[0], null)
}

output "master_db_instance_resource_id" {
  description = "The RDS Resource ID of this instance"
  value       = module.master.db_instance_resource_id
}

output "master_db_instance_status" {
  description = "The RDS instance status"
  value       = module.master.db_instance_status
}

output "master_db_instance_name" {
  description = "The database name"
  value       = module.master.db_instance_name
}

output "master_db_instance_username" {
  description = "The master username for the database"
  value       = module.master.db_instance_username
  sensitive   = true
}

output "master_db_instance_password" {
  description = "The database password (this password may be old, because Terraform doesn't track it after initial creation)"
  value       = module.master.db_instance_password
  sensitive   = true
}

output "master_db_instance_port" {
  description = "The database port"
  value       = module.master.db_instance_port
}

output "master_db_subnet_group_id" {
  description = "The db subnet group name"
  value       = module.master.db_subnet_group_id
}

output "master_db_subnet_group_arn" {
  description = "The ARN of the db subnet group"
  value       = module.master.db_subnet_group_arn
}

# Replica
output "replica_db_instance_address" {
  description = "The address of the RDS instance"
  value       = module.replica.db_instance_address
}

output "replica_db_instance_arn" {
  description = "The ARN of the RDS instance"
  value       = module.replica.db_instance_arn
}

output "replica_db_instance_availability_zone" {
  description = "The availability zone of the RDS instance"
  value       = module.replica.db_instance_availability_zone
}

output "replica_db_instance_endpoint" {
  description = "The connection endpoint"
  value       = module.replica.db_instance_endpoint
}

output "replica_db_instance_hosted_zone_id" {
  description = "The canonical hosted zone ID of the DB instance (to be used in a Route 53 Alias record)"
  value       = module.replica.db_instance_hosted_zone_id
}

output "replica_db_instance_id" {
  description = "The RDS instance ID"
  value       = module.replica.db_instance_id
}

output "replica_db_instance_identifier" {
  description = "The RDS replica DB instance identifier"
  # Module "terraform-aws-modules/rds/aws:5.2.0" doesn't output DBInstanceIdentifier, hence
  # extracting from ARN.
  value = try(regex(".*:db:([^:]+)$", module.replica.db_instance_arn)[0], null)
}

output "replica_db_instance_resource_id" {
  description = "The RDS Resource ID of this instance"
  value       = module.replica.db_instance_resource_id
}

output "replica_db_instance_status" {
  description = "The RDS instance status"
  value       = module.replica.db_instance_status
}

output "replica_db_instance_name" {
  description = "The database name"
  value       = module.replica.db_instance_name
}

output "replica_db_instance_username" {
  description = "The replica username for the database"
  value       = module.replica.db_instance_username
  sensitive   = true

}

output "replica_db_instance_port" {
  description = "The database port"
  value       = module.replica.db_instance_port
}

# IAM Outputs
output "eyecue_admin_role_name" {
  description = "The name of the IAM role for Eyecue Admin"
  value       = aws_iam_role.eyecue_admin_role.name
}

output "eyecue_admin_role_arn" {
  description = "The ARN of the IAM role for Eyecue Admin"
  value       = aws_iam_role.eyecue_admin_role.arn
}

output "eyecue_admin_policy_arn" {
  description = "The ARN of the IAM policy for Eyecue Admin"
  value       = aws_iam_policy.eyecue_admin_policy.arn
}

# Security Group Outputs
output "security_group_id" {
  description = "The ID of the main security group for RDS access"
  value       = aws_security_group.main_sec_group.id
}

output "security_group_arn" {
  description = "The ARN of the main security group for RDS access"
  value       = aws_security_group.main_sec_group.arn
}

# RDS Access Role Outputs (from internal module)
output "rds_access_role_arn" {
  description = "The ARN of the RDS read access role"
  value       = module.rds_access_role.role_arn
}

output "rds_access_role_name" {
  description = "The name of the RDS read access role"
  value       = module.rds_access_role.role_name
}

output "rds_access_policy_arn" {
  description = "The ARN of the RDS Lambda access policy"
  value       = module.rds_access_role.policy_arn
}

# Password Output (sensitive)
output "master_password_generated" {
  description = "Whether a random password was generated for the master database"
  value       = var.create_random_password
}

# DNS Records - DEPRECATED
# These outputs are maintained for backward compatibility but return null
# The Cloudflare DNS modules have been disabled for security reasons
output "master_dns_hostname" {
  description = "DEPRECATED - DNS records are no longer created for security reasons"
  value       = null
}

output "replica_dns_hostname" {
  description = "DEPRECATED - DNS records are no longer created for security reasons"
  value       = null
}

# Database Configuration Outputs
output "database_engine" {
  description = "The database engine"
  value       = var.rds_engine
}

output "database_engine_version" {
  description = "The database engine version"
  value       = var.rds_engine_version
}

output "database_port" {
  description = "The database port"
  value       = var.rds_port
}

output "database_name" {
  description = "The name of the database"
  value       = var.rds_name
}

output "database_username" {
  description = "The master username for the database"
  value       = var.rds_username
  sensitive   = true
}

# Storage Configuration Outputs
output "storage_encrypted" {
  description = "Whether the database storage is encrypted"
  value       = var.rds_storage_encrypted
}

output "storage_type" {
  description = "The storage type of the database"
  value       = var.rds_storage_type
}

output "allocated_storage" {
  description = "The allocated storage in GBs"
  value       = var.rds_allocated_storage
}

output "max_allocated_storage" {
  description = "The upper limit to which RDS can automatically scale the storage"
  value       = var.rds_max_allocated_storage
}

# Backup Configuration Outputs
output "backup_retention_period" {
  description = "The backup retention period in days"
  value       = var.rds_backup_retention_period
}

output "backup_window" {
  description = "The backup window"
  value       = var.rds_backup_window
}

output "maintenance_window" {
  description = "The maintenance window"
  value       = var.rds_maintenance_window
}

# Network Configuration Outputs
output "vpc_id" {
  description = "The VPC ID where the RDS instance is deployed"
  value       = var.vpc_id
}

output "subnet_ids" {
  description = "The subnet IDs where the RDS instance is deployed"
  value       = var.subnet_ids
}

# Parameter Group Outputs
output "parameter_group_created" {
  description = "Whether a custom parameter group was created"
  value       = var.create_db_parameter_group
}

output "parameter_group_name" {
  description = "The name of the parameter group"
  value       = var.create_db_parameter_group ? module.master.db_parameter_group_id : var.parameter_group_name
}

output "parameter_group_family" {
  description = "The parameter group family"
  value       = var.parameter_group_family
}

# Replica Configuration
output "replica_enabled" {
  description = "Whether a read replica is created"
  value       = var.create_replica
}

# CA Certificate
output "ca_cert_identifier" {
  description = "The identifier of the CA certificate for the DB instance"
  value       = var.rds_ca_cert_identifier
}

# Deletion Protection
output "deletion_protection_enabled" {
  description = "Whether deletion protection is enabled"
  value       = var.deletion_protection
}

# Performance Insights
output "performance_insights_enabled" {
  description = "Whether Performance Insights is enabled"
  value       = var.rds_performance_insights_enabled
}

# IAM Database Authentication
output "iam_database_authentication_enabled" {
  description = "Whether IAM database authentication is enabled"
  value       = true
}
