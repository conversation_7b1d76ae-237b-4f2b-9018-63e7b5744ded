module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess", "DbaAccess"]
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "eyecue_roi_suggestor" {
  source         = "../../../../modules/eyecue_sqs_provider"
  aws_iam_user   = "roisuggestor"
  client_name    = var.CLIENT_NAME
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
  country        = var.COUNTRY
  service_name   = "roisuggestor"
}

module "eyecue_mosaic" {
  source         = "../../../../modules/eyecue_sqs_provider"
  aws_iam_user   = "eyecue-mosaic"
  client_name    = var.CLIENT_NAME
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
  country        = var.COUNTRY
  service_name   = "mosaic"
}

module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "eyecue_assembler" {
  source         = "../../../../modules/eyecue_assembler"
  aws_iam_user   = "eyecue-assembler"
  client_name    = var.CLIENT_NAME
  aws_account_id = data.aws_caller_identity.current
  aws_region     = var.AWS_REGION
  country        = var.COUNTRY
}

module "eyecue_rds" {
  source                 = "../../../../modules/eyecue_rds"
  product                = "Eyecue"
  aws_region             = var.AWS_REGION
  aws_account_id         = data.aws_caller_identity.current.account_id
  eyecue_rds_customer_id = var.CLIENT_ACRONYM

  vpc_id                          = module.eyecue_network.vpc_id
  subnet_ids                      = module.eyecue_network.public_subnet_ids
  vpc_security_group_ids          = ["sg-020167877660f2e1e"]
  vpc_security_group_name         = "EYECue DB"
  vpc_security_group_description  = "EYECue DB Security Group"
  rds_create_db_subnet_group      = false
  db_subnet_group_use_name_prefix = false
  db_subnet_group_name            = "default"

  rds_instance_identifier       = "eyecue"
  rds_username                  = "fingermark"
  create_random_password        = false
  rds_create_random_password    = false
  rds_master_instance_class     = "db.m5.large"
  rds_engine_version            = "16.8"
  rds_ca_cert_identifier        = "rds-ca-rsa2048-g1"
  rds_allocated_storage         = 1735
  rds_max_allocated_storage     = 2000
  rds_master_iops               = 12000
  rds_storage_encrypted         = false
  rds_apply_changes_immediately = false
  rds_skip_final_snapshot       = true
  rds_backup_retention_period   = 7
  rds_backup_window             = "13:00-13:30"
  rds_maintenance_window        = "Wed:15:15-Wed:15:45"
  rds_timeout                   = {}

  create_db_parameter_group       = true
  parameter_group_family          = "postgres16"
  parameter_group_name            = "eyecye-postgres16" # Yes the typo is intended
  parameter_group_use_name_prefix = false
  parameter_group_description     = "master-postgres parameter group"
  parameter_group_parameters = [ # Explicitly disable forced SSL
    {
      name  = "rds.force_ssl"
      value = "0"
    }
  ]

  rds_performance_insights_enabled = true
  create_replica                   = false
  allow_major_version_upgrade      = false

  eyecue_rds_cloudflare_api_key = data.vault_generic_secret.cloudflare.data["api_key"]
  special_password              = false
}

module "secret_manager" {
  source = "../../../../modules/secret_manager"

  eyecue_postgres_lambdas_secret_name = "rds/ssm/eyecue-postgres-lambdas"

  eyecue_dashboard_data_secret_name = "rds/ssm/eyecue-dashboard-data"
}

module "icinga2_satellite" {
  source                                         = "../../../../modules/icinga2_satellite"
  icinga2_satellite_vpc_id                       = module.eyecue_network.vpc_id
  icinga2_satellite_ec2_ami_id                   = "ami-0567f647e75c7bc05"
  icinga2_satellite_ec2_subnet_id                = module.eyecue_network.public_subnet_ids[1]
  icinga2_satellite_ec2_extra_security_group_ids = [module.eyecue_network.havelock_security_group_id, "sg-02e59a79"]
  icinga2_satellite_customer_id                  = var.CLIENT_ACRONYM
  icinga2_satellite_cloudflare_api_key           = data.vault_generic_secret.cloudflare.data["api_key"]
  icinga2_satellite_ec2_instance_type            = "m5.xlarge"
  icinga2_satellite_ebs_optimized                = true
  icinga2_satellite_ec2_ssh_key_name             = "matias"
}

module "eyecue_mimir" {
  source                   = "../../../../modules/eyecue_mimir"
  mimir_aws_account_id     = "************"
  lambda_role_arn_suffixes = var.lambda_role_arn_suffixes
  policy_description       = "Policy to invoke request-roisuggestor-png lambda from cross accounts."
  AWS_ACCOUNT_ID           = data.aws_caller_identity.current.account_id
  AWS_REGION               = var.AWS_REGION
}

module "eyecue_camera_metrics_exporter" {
  source         = "../../../../modules/eyecue_camera_metrics_exporter"
  aws_region     = var.AWS_REGION
  aws_account_id = data.aws_caller_identity.current.account_id
  keybase        = var.KEYBASE
  tags           = var.default_tags
}

module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "eyecue_dashboard_iot" {
  source                 = "../../../../modules/eyecue_dashboard_iot"
  aws_iam_user           = "eyecue-dashboard-iot"
  aws_iam_user_read_only = "eyecue-dashboard-iot-read-only"
  aws_account_id         = data.aws_caller_identity.current.account_id
  aws_region             = var.AWS_REGION
}

############################################################################################
#Kinesis to Redshift integration - new kinesis structure :
#TODO:
#   1. Create a new kinesis stream (journey, drivethru-events)
#   2. Migrate data model to new structure in redshift (data-terraform)
#   4. Cleanup old kinesis streams

#Kinesis Streams

module "kinesis_redshift_journey_data_stream" {
  source                             = "../../../../modules/kinesis_data_stream"
  aws_region                         = var.AWS_REGION
  client_name                        = var.kinesis_client_name
  redshift_aws_account_ids_roles     = var.redshift_aws_account_ids_roles
  retention_period                   = "72"
  stream_mode                        = "ON_DEMAND"
  stream_name_list                   = ["journey"]
  current_account_id                 = data.aws_caller_identity.current.account_id
  create_role                        = true
  is_data_sharing_enabled            = false
  redshift_stream_access_role_name   = "redshift_stream_access_role"
  redshift_stream_access_policy_name = "kinesis_stream_policy"
}

module "kinesis_redshift_drivethru_events_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.kinesis_client_name
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = "72"
  stream_mode                    = "ON_DEMAND"
  stream_name_list               = ["drivethru-events"]
  current_account_id             = data.aws_caller_identity.current.account_id
  create_role                    = false
  is_data_sharing_enabled        = false
}

module "eyecue_iot_kinesis_rules" {
  source                                             = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                                     = var.kinesis_client_name
  kinesis_iot_topic_rules_config                     = local.kinesis_iot_topic_rules_config
  fallback_bucket_name                               = "fm-data-eyecue-kinesis-failure-ap-southeast-2"
  enable_fallback_to_s3                              = true
  iot_kinesis_eventstream_iam_role_name              = "mcd-aus-iot-kinesis-events-role"
  iot_kinesis_eventstream_iam_policy_name            = "mcd-aus-iot-kinesis-event-policy"
  iot_kinesis_cloudwatch_log_group_name              = "mcd_aus_iot_kinesis"
  iot_kinesis_cloudwatch_log_group_iam_policy_name   = "mcd-aus-iot-kinesis-cloudwatch-access-policy"
  iot_kinesis_eventstream_iam_policy_attachment_name = "mcd-aus-iot-kinesis-event-logs-attachment"
  iot_kinesis_fallback_lambda_iam_role_name          = "mcd-aus-iot-kinesis-fallback-lambda-role"
  iot_kinesis_fallback_lambda_iam_policy_name        = "mcd-aus-iot-kinesis-fallback-lambda-policy"
  iot_kinesis_fallback_lambda_function_name          = "mcd-aus-iot-kinesis-fallback-lambda"
}


### OLD MODULES to be removed
module "kinesis_data_stream_aggregate" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_NAME
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.kinesis_data_stream_retention_period
  stream_mode                    = var.kinesis_data_stream_stream_mode
  stream_name_list               = ["aggregate"]
  current_account_id             = data.aws_caller_identity.current.account_id
  is_data_sharing_enabled        = false
  create_role                    = true
}

module "kinesis_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_NAME
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.kinesis_data_stream_retention_period
  stream_mode                    = var.kinesis_data_stream_stream_mode
  stream_name_list               = ["eventstream"]
  current_account_id             = data.aws_caller_identity.current.account_id
  create_role                    = false
}

module "eyecue_iot_kinesis_eventstream" {
  source                         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                 = var.CLIENT_ACRONYM
  kinesis_iot_topic_rules_config = local.kinesis_iot_topic_rules_config_old
  is_data_sharing_enabled        = true
  fallback_bucket_name           = "fm-data-eyecue-kinesis-failure-ap-southeast-2"
  enable_fallback_to_s3          = true
  client_name                    = var.CLIENT_NAME
}

#############################################################################################

locals {
  image_sync_user = {
    name = "lpr_sync"
    arn  = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/lpr_sync"
  }
}

module "eyecue_validation_s3_access" {
  source         = "../../../../modules/eyecue_validation_s3_access"
  aws_iam_user   = local.image_sync_user
  client_name    = var.CLIENT_NAME
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
  country        = var.COUNTRY
  bucket_arn     = "arn:aws:s3:::eyecue-mcdonalds-images"
  bucket_name    = "eyecue-mcdonalds-images"
  bucket_prefix  = "validation-tool-images"
  prefix_expiry  = 30
  service_name   = "ValidationToolImages"
}

module "camera_displacement" {
  source = "../../../../modules/eyecue_camera_displacement"

  customer                 = "${var.CLIENT_NAME}-${var.COUNTRY_FULL}"
  region                   = var.AWS_REGION
  account_id               = data.aws_caller_identity.current.account_id
  images_bucket_arn        = "arn:aws:s3:::eyecue-mcdonalds-images"
  camera_images_bucket_arn = "arn:aws:s3:::eyecue-mcdonalds-aus-camera-images"
  slack_webhook_url        = "https://hooks.slack.com/triggers/T0CMMNY4C/*************/878d614524952ace6c75f0cf9248499e"
  image_sync_user          = local.image_sync_user.name

  tags = {
    Customer    = "${var.CLIENT_NAME}-${var.COUNTRY_FULL}"
    Squad       = "Vision"
    Environment = var.CLIENT_NAME != "cv-qa" ? "production" : "qa"
    Product     = "Eyecue"
    Terraform   = "True"
  }
}

resource "aws_iam_policy" "camera_displacement_policy" {
  name        = "CameraDisplacementIAMPolicy"
  description = "Additional permissions for the camera displacement module"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        "Action" : [
          "sqs:SendMessage",
          "sqs:GetQueueUrl",
          "sqs:GetQueueAttributes"
        ],
        "Effect" : "Allow",
        "Resource" : "arn:aws:sqs:${var.AWS_REGION}:${data.aws_caller_identity.current.account_id}:eyecue-camera-displacement-sqs-${var.CLIENT_ACRONYM}-${var.COUNTRY_FULL}",
        "Sid" : "EyecueCameraDisplacementSendMessagePolicy"
      },
      {
        "Action" : "kms:GenerateDataKey",
        "Effect" : "Allow",
        "Resource" : "${module.camera_displacement.kms_arn}",
        "Sid" : "EyecueCameraDisplacementKMS"
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "image_sync_user_cam_displacement_attachment" {
  user       = local.image_sync_user.name
  policy_arn = aws_iam_policy.camera_displacement_policy.arn
}

# ==========================================
# SOC2 Security
# ==========================================

module "vanta" {
  source = "../../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source        = "../../../../modules/sqs_monitoring"
  sns_topic_arn = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
}

# ------- S3 Public Access Block -------

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ------ CloudWatch Log Retention Management ------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================
locals {
  rds_cw_alarms = {
    # `module.eyecue_rds` doesn't exist, hence hardcoding RDS DB instance values
    eyecue_rds = {
      master_db_instance_name       = "eyeq"
      master_db_instance_identifier = "eyecue"
    }
  }
}

module "rds_cw_alarms" {
  source                                 = "../../../../modules/rds_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_rds_cpu_util           = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_mem_free           = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_disk_queue_depth   = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_write_iops         = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_read_iops          = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_free_storage_space = { (local.rds_cw_alarms.eyecue_rds.master_db_instance_name) = { identifier = local.rds_cw_alarms.eyecue_rds.master_db_instance_identifier } }
  tags                                   = var.tags
  default_tags                           = var.default_tags
}

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "grafana"           = { instance_tags = { Name = "grafana" } }
    "icinga2-satellite" = { instance_tags = { Name = "icinga2-satellite" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}
